import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:animate_do/animate_do.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _autoPlayAudio = false;
  bool _smartReminders = true;
  double _fontSize = 16.0;
  String _selectedLanguage = 'العربية';
  String _selectedReciter = 'الحصري';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F8E9),
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: GoogleFonts.amiri(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المظهر'),
            _buildSettingsCard([
              _buildSwitchTile(
                title: 'الوضع الليلي',
                subtitle: 'تفعيل المظهر الداكن',
                icon: Icons.dark_mode,
                value: _isDarkMode,
                onChanged: (value) {
                  setState(() {
                    _isDarkMode = value;
                  });
                },
              ),
              _buildSliderTile(
                title: 'حجم الخط',
                subtitle: 'تغيير حجم النص',
                icon: Icons.text_fields,
                value: _fontSize,
                min: 12.0,
                max: 24.0,
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                },
              ),
              _buildDropdownTile(
                title: 'اللغة',
                subtitle: 'اختيار لغة التطبيق',
                icon: Icons.language,
                value: _selectedLanguage,
                items: ['العربية', 'English'],
                onChanged: (value) {
                  setState(() {
                    _selectedLanguage = value!;
                  });
                },
              ),
            ]),
            
            const SizedBox(height: 20),
            _buildSectionTitle('الإشعارات'),
            _buildSettingsCard([
              _buildSwitchTile(
                title: 'تفعيل الإشعارات',
                subtitle: 'استقبال تذكيرات الأذكار والصلوات',
                icon: Icons.notifications,
                value: _notificationsEnabled,
                onChanged: (value) {
                  setState(() {
                    _notificationsEnabled = value;
                  });
                },
              ),
              _buildSwitchTile(
                title: 'الصوت',
                subtitle: 'تشغيل الأصوات مع الإشعارات',
                icon: Icons.volume_up,
                value: _soundEnabled,
                onChanged: (value) {
                  setState(() {
                    _soundEnabled = value;
                  });
                },
              ),
              _buildSwitchTile(
                title: 'الاهتزاز',
                subtitle: 'تفعيل الاهتزاز مع الإشعارات',
                icon: Icons.vibration,
                value: _vibrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _vibrationEnabled = value;
                  });
                },
              ),
            ]),
            
            const SizedBox(height: 20),
            _buildSectionTitle('الصوتيات'),
            _buildSettingsCard([
              _buildSwitchTile(
                title: 'التشغيل التلقائي',
                subtitle: 'تشغيل الصوت تلقائياً عند فتح الذكر',
                icon: Icons.play_circle,
                value: _autoPlayAudio,
                onChanged: (value) {
                  setState(() {
                    _autoPlayAudio = value;
                  });
                },
              ),
              _buildDropdownTile(
                title: 'القارئ المفضل',
                subtitle: 'اختيار صوت القارئ',
                icon: Icons.record_voice_over,
                value: _selectedReciter,
                items: ['الحصري', 'العفاسي', 'المنشاوي', 'عبد الباسط'],
                onChanged: (value) {
                  setState(() {
                    _selectedReciter = value!;
                  });
                },
              ),
            ]),
            
            const SizedBox(height: 20),
            _buildSectionTitle('الذكاء الاصطناعي'),
            _buildSettingsCard([
              _buildSwitchTile(
                title: 'التذكيرات الذكية',
                subtitle: 'تذكيرات مخصصة حسب نشاطك',
                icon: Icons.psychology,
                value: _smartReminders,
                onChanged: (value) {
                  setState(() {
                    _smartReminders = value;
                  });
                },
              ),
            ]),
            
            const SizedBox(height: 20),
            _buildSectionTitle('معلومات'),
            _buildSettingsCard([
              _buildInfoTile(
                title: 'إصدار التطبيق',
                subtitle: '1.0.0',
                icon: Icons.info,
              ),
              _buildInfoTile(
                title: 'تقييم التطبيق',
                subtitle: 'ساعدنا بتقييمك',
                icon: Icons.star,
                onTap: () {
                  // Open app store for rating
                },
              ),
              _buildInfoTile(
                title: 'تواصل معنا',
                subtitle: 'أرسل ملاحظاتك واقتراحاتك',
                icon: Icons.email,
                onTap: () {
                  // Open email or contact form
                },
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return FadeInLeft(
      duration: const Duration(milliseconds: 600),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 15),
        child: Text(
          title,
          style: GoogleFonts.amiri(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2E7D32),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: children,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFF4CAF50),
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.amiri(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.amiri(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF4CAF50),
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required double value,
    required double min,
    required double max,
    required ValueChanged<double> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFF4CAF50),
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.amiri(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: GoogleFonts.amiri(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: ((max - min) / 2).round(),
            label: value.round().toString(),
            onChanged: onChanged,
            activeColor: const Color(0xFF4CAF50),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFF4CAF50),
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.amiri(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.amiri(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.amiri(fontSize: 14),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        underline: Container(),
      ),
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String subtitle,
    required IconData icon,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFF4CAF50),
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.amiri(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.amiri(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: onTap != null 
          ? const Icon(Icons.arrow_forward_ios, size: 16)
          : null,
      onTap: onTap,
    );
  }
}
