import 'package:intl/intl.dart';

class PrayerTimeModel {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime date;
  final String location;

  PrayerTimeModel({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    required this.date,
    required this.location,
  });

  Map<String, dynamic> toMap() {
    return {
      'fajr': fajr.millisecondsSinceEpoch,
      'sunrise': sunrise.millisecondsSinceEpoch,
      'dhuhr': dhuhr.millisecondsSinceEpoch,
      'asr': asr.millisecondsSinceEpoch,
      'maghrib': maghrib.millisecondsSinceEpoch,
      'isha': isha.millisecondsSinceEpoch,
      'date': date.millisecondsSinceEpoch,
      'location': location,
    };
  }

  factory PrayerTimeModel.fromMap(Map<String, dynamic> map) {
    return PrayerTimeModel(
      fajr: DateTime.fromMillisecondsSinceEpoch(map['fajr']),
      sunrise: DateTime.fromMillisecondsSinceEpoch(map['sunrise']),
      dhuhr: DateTime.fromMillisecondsSinceEpoch(map['dhuhr']),
      asr: DateTime.fromMillisecondsSinceEpoch(map['asr']),
      maghrib: DateTime.fromMillisecondsSinceEpoch(map['maghrib']),
      isha: DateTime.fromMillisecondsSinceEpoch(map['isha']),
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      location: map['location'] ?? '',
    );
  }

  String getNextPrayerName() {
    final now = DateTime.now();
    final prayers = [
      {'name': 'الفجر', 'time': fajr},
      {'name': 'الشروق', 'time': sunrise},
      {'name': 'الظهر', 'time': dhuhr},
      {'name': 'العصر', 'time': asr},
      {'name': 'المغرب', 'time': maghrib},
      {'name': 'العشاء', 'time': isha},
    ];

    for (var prayer in prayers) {
      if (now.isBefore(prayer['time'] as DateTime)) {
        return prayer['name'] as String;
      }
    }
    return 'الفجر'; // Next day Fajr
  }

  DateTime getNextPrayerTime() {
    final now = DateTime.now();
    final prayers = [fajr, sunrise, dhuhr, asr, maghrib, isha];

    for (var prayer in prayers) {
      if (now.isBefore(prayer)) {
        return prayer;
      }
    }
    // Return next day Fajr
    return fajr.add(const Duration(days: 1));
  }

  Duration getTimeUntilNextPrayer() {
    final nextPrayer = getNextPrayerTime();
    return nextPrayer.difference(DateTime.now());
  }

  String formatTime(DateTime time) {
    return DateFormat('HH:mm').format(time);
  }

  Map<String, String> getAllPrayerTimes() {
    return {
      'الفجر': formatTime(fajr),
      'الشروق': formatTime(sunrise),
      'الظهر': formatTime(dhuhr),
      'العصر': formatTime(asr),
      'المغرب': formatTime(maghrib),
      'العشاء': formatTime(isha),
    };
  }
}

class PrayerNotification {
  final String prayerName;
  final DateTime time;
  final bool isEnabled;
  final int minutesBefore;

  PrayerNotification({
    required this.prayerName,
    required this.time,
    this.isEnabled = true,
    this.minutesBefore = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'prayerName': prayerName,
      'time': time.millisecondsSinceEpoch,
      'isEnabled': isEnabled ? 1 : 0,
      'minutesBefore': minutesBefore,
    };
  }

  factory PrayerNotification.fromMap(Map<String, dynamic> map) {
    return PrayerNotification(
      prayerName: map['prayerName'] ?? '',
      time: DateTime.fromMillisecondsSinceEpoch(map['time']),
      isEnabled: map['isEnabled'] == 1,
      minutesBefore: map['minutesBefore'] ?? 0,
    );
  }
}
