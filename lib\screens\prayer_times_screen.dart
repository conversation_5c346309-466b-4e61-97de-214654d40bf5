import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:animate_do/animate_do.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:math' as math;

class PrayerTimesScreen extends StatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  State<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends State<PrayerTimesScreen>
    with TickerProviderStateMixin {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();
  late AnimationController _clockController;
  
  // Mock prayer times - في التطبيق الحقيقي ستأتي من API أو حساب
  final Map<String, DateTime> _prayerTimes = {
    'الفجر': DateTime.now().copyWith(hour: 5, minute: 30),
    'الشروق': DateTime.now().copyWith(hour: 6, minute: 45),
    'الظهر': DateTime.now().copyWith(hour: 12, minute: 15),
    'العصر': DateTime.now().copyWith(hour: 15, minute: 30),
    'المغرب': DateTime.now().copyWith(hour: 18, minute: 45),
    'العشاء': DateTime.now().copyWith(hour: 20, minute: 15),
  };

  @override
  void initState() {
    super.initState();
    _clockController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();
    
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _clockController.dispose();
    super.dispose();
  }

  String _getNextPrayer() {
    final now = DateTime.now();
    for (var entry in _prayerTimes.entries) {
      if (now.isBefore(entry.value)) {
        return entry.key;
      }
    }
    return 'الفجر'; // Next day
  }

  Duration _getTimeUntilNextPrayer() {
    final now = DateTime.now();
    final nextPrayer = _getNextPrayer();
    var nextPrayerTime = _prayerTimes[nextPrayer]!;
    
    if (now.isAfter(nextPrayerTime)) {
      nextPrayerTime = nextPrayerTime.add(const Duration(days: 1));
    }
    
    return nextPrayerTime.difference(now);
  }

  Color _getPrayerColor(String prayer) {
    switch (prayer) {
      case 'الفجر':
        return const Color(0xFF2196F3);
      case 'الشروق':
        return const Color(0xFFFF9800);
      case 'الظهر':
        return const Color(0xFF4CAF50);
      case 'العصر':
        return const Color(0xFFFF5722);
      case 'المغرب':
        return const Color(0xFF9C27B0);
      case 'العشاء':
        return const Color(0xFF3F51B5);
      default:
        return const Color(0xFF4CAF50);
    }
  }

  IconData _getPrayerIcon(String prayer) {
    switch (prayer) {
      case 'الفجر':
        return Icons.wb_twilight;
      case 'الشروق':
        return Icons.wb_sunny;
      case 'الظهر':
        return Icons.wb_sunny_outlined;
      case 'العصر':
        return Icons.wb_incandescent;
      case 'المغرب':
        return Icons.nights_stay;
      case 'العشاء':
        return Icons.bedtime;
      default:
        return Icons.access_time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final nextPrayer = _getNextPrayer();
    final timeUntilNext = _getTimeUntilNextPrayer();
    
    return Scaffold(
      backgroundColor: const Color(0xFFF1F8E9),
      appBar: AppBar(
        title: Text(
          'أوقات الصلاة',
          style: GoogleFonts.amiri(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildCurrentTimeCard(),
            const SizedBox(height: 20),
            _buildNextPrayerCard(nextPrayer, timeUntilNext),
            const SizedBox(height: 30),
            _buildPrayerTimesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentTimeCard() {
    return FadeInDown(
      duration: const Duration(milliseconds: 800),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF4CAF50),
              const Color(0xFF4CAF50).withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              'الوقت الحالي',
              style: GoogleFonts.amiri(
                fontSize: 18,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
            const SizedBox(height: 10),
            AnimatedBuilder(
              animation: _clockController,
              builder: (context, child) {
                return Text(
                  DateFormat('HH:mm:ss').format(_currentTime),
                  style: GoogleFonts.orbitron(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 2,
                  ),
                );
              },
            ),
            const SizedBox(height: 5),
            Text(
              DateFormat('EEEE، d MMMM yyyy', 'ar').format(_currentTime),
              style: GoogleFonts.amiri(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextPrayerCard(String nextPrayer, Duration timeUntilNext) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1000),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(25),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: _getPrayerColor(nextPrayer).withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getPrayerIcon(nextPrayer),
                  color: _getPrayerColor(nextPrayer),
                  size: 30,
                ),
                const SizedBox(width: 10),
                Text(
                  'الصلاة القادمة: $nextPrayer',
                  style: GoogleFonts.amiri(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _getPrayerColor(nextPrayer),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Text(
              DateFormat('HH:mm').format(_prayerTimes[nextPrayer]!),
              style: GoogleFonts.orbitron(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'متبقي: ${timeUntilNext.inHours.toString().padLeft(2, '0')}:${(timeUntilNext.inMinutes % 60).toString().padLeft(2, '0')}:${(timeUntilNext.inSeconds % 60).toString().padLeft(2, '0')}',
              style: GoogleFonts.amiri(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FadeInLeft(
          duration: const Duration(milliseconds: 1200),
          child: Text(
            'جميع أوقات الصلاة',
            style: GoogleFonts.amiri(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2E7D32),
            ),
          ),
        ),
        const SizedBox(height: 20),
        ...List.generate(
          _prayerTimes.length,
          (index) {
            final entry = _prayerTimes.entries.elementAt(index);
            final isNext = entry.key == _getNextPrayer();
            
            return FadeInRight(
              duration: Duration(milliseconds: 1400 + (index * 100)),
              child: Container(
                margin: const EdgeInsets.only(bottom: 15),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isNext 
                      ? _getPrayerColor(entry.key).withValues(alpha: 0.1)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  border: isNext 
                      ? Border.all(
                          color: _getPrayerColor(entry.key),
                          width: 2,
                        )
                      : null,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _getPrayerColor(entry.key).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getPrayerIcon(entry.key),
                        color: _getPrayerColor(entry.key),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Text(
                        entry.key,
                        style: GoogleFonts.amiri(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isNext 
                              ? _getPrayerColor(entry.key)
                              : Colors.grey[800],
                        ),
                      ),
                    ),
                    Text(
                      DateFormat('HH:mm').format(entry.value),
                      style: GoogleFonts.orbitron(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isNext 
                            ? _getPrayerColor(entry.key)
                            : Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
