import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:workmanager/workmanager.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = 
      FlutterLocalNotificationsPlugin();
  
  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // طلب الأذونات
    await _requestPermissions();

    // تهيئة الإشعارات المحلية
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // تهيئة العمل في الخلفية (للويب قد لا يعمل)
    if (!kIsWeb) {
      await _initializeWorkManager();
    }

    _isInitialized = true;
  }

  static Future<void> _requestPermissions() async {
    if (!kIsWeb) {
      await Permission.notification.request();
    }
  }

  static Future<void> _initializeWorkManager() async {
    await Workmanager().initialize(
      _callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
  }

  /// معالج النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// إرسال إشعار فوري
  static Future<void> showInstantNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'dhikr_channel',
      'أذكار',
      channelDescription: 'إشعارات الأذكار والتذكيرات',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      DateTime.now().millisecond,
      title,
      body,
      details,
      payload: payload,
    );
  }

  /// جدولة إشعار لوقت محدد
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'dhikr_scheduled',
      'أذكار مجدولة',
      channelDescription: 'إشعارات الأذكار المجدولة',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      scheduledTime,
      details,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  /// جدولة إشعارات الصلاة
  static Future<void> schedulePrayerNotifications(
    Map<String, DateTime> prayerTimes,
  ) async {
    // إلغاء الإشعارات السابقة
    await cancelPrayerNotifications();

    int notificationId = 1000;
    for (final entry in prayerTimes.entries) {
      final prayerName = entry.key;
      final prayerTime = entry.value;

      // إشعار قبل الصلاة بـ 10 دقائق
      final reminderTime = prayerTime.subtract(const Duration(minutes: 10));
      if (reminderTime.isAfter(DateTime.now())) {
        await scheduleNotification(
          id: notificationId++,
          title: 'تذكير الصلاة',
          body: 'حان وقت صلاة $prayerName خلال 10 دقائق',
          scheduledTime: reminderTime,
          payload: 'prayer_reminder_$prayerName',
        );
      }

      // إشعار وقت الصلاة
      if (prayerTime.isAfter(DateTime.now())) {
        await scheduleNotification(
          id: notificationId++,
          title: 'وقت الصلاة',
          body: 'حان الآن وقت صلاة $prayerName',
          scheduledTime: prayerTime,
          payload: 'prayer_time_$prayerName',
        );
      }
    }
  }

  /// جدولة تذكيرات الأذكار اليومية
  static Future<void> scheduleDailyDhikrReminders() async {
    await cancelDhikrReminders();

    final now = DateTime.now();
    int notificationId = 2000;

    // أذكار الصباح - 7:00 صباحاً
    final morningTime = DateTime(now.year, now.month, now.day, 7, 0);
    if (morningTime.isAfter(now)) {
      await scheduleNotification(
        id: notificationId++,
        title: 'أذكار الصباح',
        body: 'ابدأ يومك بأذكار الصباح المباركة',
        scheduledTime: morningTime,
        payload: 'morning_dhikr',
      );
    }

    // أذكار المساء - 6:00 مساءً
    final eveningTime = DateTime(now.year, now.month, now.day, 18, 0);
    if (eveningTime.isAfter(now)) {
      await scheduleNotification(
        id: notificationId++,
        title: 'أذكار المساء',
        body: 'وقت أذكار المساء المباركة',
        scheduledTime: eveningTime,
        payload: 'evening_dhikr',
      );
    }

    // تذكير عام - 2:00 ظهراً
    final afternoonTime = DateTime(now.year, now.month, now.day, 14, 0);
    if (afternoonTime.isAfter(now)) {
      await scheduleNotification(
        id: notificationId++,
        title: 'تذكير الأذكار',
        body: 'لا تنس ذكر الله في هذا الوقت المبارك',
        scheduledTime: afternoonTime,
        payload: 'general_dhikr',
      );
    }
  }

  /// إلغاء إشعارات الصلاة
  static Future<void> cancelPrayerNotifications() async {
    for (int i = 1000; i < 1100; i++) {
      await _notifications.cancel(i);
    }
  }

  /// إلغاء تذكيرات الأذكار
  static Future<void> cancelDhikrReminders() async {
    for (int i = 2000; i < 2100; i++) {
      await _notifications.cancel(i);
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// الحصول على الإشعارات المعلقة
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }
}

/// معالج العمل في الخلفية
@pragma('vm:entry-point')
void _callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case 'dhikr_reminder':
        await NotificationService.showInstantNotification(
          title: 'تذكير الأذكار',
          body: 'حان وقت الذكر والتسبيح',
          payload: 'background_reminder',
        );
        break;
      case 'daily_stats':
        // يمكن إضافة منطق الإحصائيات اليومية هنا
        break;
    }
    return Future.value(true);
  });
}
