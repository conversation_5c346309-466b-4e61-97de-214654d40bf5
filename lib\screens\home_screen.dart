import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// import 'package:hijri/hijri.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:async';
import 'dart:math' as math;

import '../models/dhikr_model.dart';
import '../data/dhikr_database.dart';
import 'dhikr_screen.dart';
import 'prayer_times_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();
  // HijriCalendar _hijriDate = HijriCalendar.now();
  String _hijriDate = '';
  
  late AnimationController _clockController;
  late AnimationController _gradientController;
  
  List<DhikrModel> _morningDhikr = [];
  List<DhikrModel> _eveningDhikr = [];
  List<DhikrModel> _generalDhikr = [];
  
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startTimer();
    _loadDhikrData();
  }

  void _initializeAnimations() {
    _clockController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();
    
    _gradientController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat(reverse: true);
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
        _hijriDate = _getHijriDate();
      });
    });
  }

  Future<void> _loadDhikrData() async {
    try {
      final morningDhikr = await DhikrDatabase.getDhikrByCategory('أذكار الصباح');
      final eveningDhikr = await DhikrDatabase.getDhikrByCategory('أذكار المساء');
      final generalDhikr = await DhikrDatabase.getDhikrByCategory('أذكار عامة');
      
      setState(() {
        _morningDhikr = morningDhikr;
        _eveningDhikr = eveningDhikr;
        _generalDhikr = generalDhikr;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    _clockController.dispose();
    _gradientController.dispose();
    super.dispose();
  }

  String _getGreeting() {
    final hour = _currentTime.hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }

  String _getHijriDate() {
    // تاريخ هجري تقريبي - في التطبيق الحقيقي يجب استخدام مكتبة دقيقة
    final now = DateTime.now();
    final hijriYear = 1446; // سنة تقريبية
    final hijriMonth = 'ذو الحجة';
    final hijriDay = 20;
    return '$hijriDay $hijriMonth $hijriYear هـ';
  }

  Color _getTimeBasedColor() {
    final hour = _currentTime.hour;
    if (hour >= 6 && hour < 12) {
      return const Color(0xFF4CAF50); // Morning green
    } else if (hour >= 12 && hour < 18) {
      return const Color(0xFF2196F3); // Afternoon blue
    } else {
      return const Color(0xFF3F51B5); // Evening indigo
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _getTimeBasedColor(),
              _getTimeBasedColor().withValues(alpha: 0.8),
              _getTimeBasedColor().withValues(alpha: 0.6),
            ],
          ),
        ),
        child: SafeArea(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 30),
                      _buildClock(),
                      const SizedBox(height: 30),
                      _buildDatesCard(),
                      const SizedBox(height: 30),
                      _buildQuickActions(),
                      const SizedBox(height: 30),
                      _buildDhikrCategories(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeInDown(
      duration: const Duration(milliseconds: 800),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getGreeting(),
                style: GoogleFonts.amiri(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'أهلاً وسهلاً بك في تطبيق أذكاري',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClock() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1000),
      child: Container(
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            AnimatedBuilder(
              animation: _clockController,
              builder: (context, child) {
                return Text(
                  DateFormat('HH:mm:ss').format(_currentTime),
                  style: GoogleFonts.orbitron(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 2,
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            Text(
              DateFormat('EEEE، d MMMM yyyy', 'ar').format(_currentTime),
              style: GoogleFonts.amiri(
                fontSize: 18,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesCard() {
    return FadeInLeft(
      duration: const Duration(milliseconds: 1200),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'التاريخ الميلادي',
                      style: GoogleFonts.amiri(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      DateFormat('d MMMM yyyy', 'ar').format(_currentTime),
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _getTimeBasedColor(),
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'التاريخ الهجري',
                      style: GoogleFonts.amiri(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      _hijriDate,
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _getTimeBasedColor(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return FadeInRight(
      duration: const Duration(milliseconds: 1400),
      child: Row(
        children: [
          Expanded(
            child: _buildActionCard(
              title: 'أوقات الصلاة',
              icon: Icons.access_time,
              color: const Color(0xFF2196F3),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const PrayerTimesScreen()),
                );
              },
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: _buildActionCard(
              title: 'المفضلة',
              icon: Icons.favorite,
              color: const Color(0xFFE91E63),
              onTap: () {
                // Navigate to favorites
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                icon,
                color: color,
                size: 30,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: GoogleFonts.amiri(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDhikrCategories() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FadeInUp(
          duration: const Duration(milliseconds: 1600),
          child: Text(
            'الأذكار',
            style: GoogleFonts.amiri(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 20),
        AnimationLimiter(
          child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                _buildDhikrCategoryCard(
                  title: 'أذكار الصباح',
                  subtitle: '${_morningDhikr.length} ذكر',
                  icon: Icons.wb_sunny,
                  color: const Color(0xFFFF9800),
                  dhikrList: _morningDhikr,
                ),
                const SizedBox(height: 15),
                _buildDhikrCategoryCard(
                  title: 'أذكار المساء',
                  subtitle: '${_eveningDhikr.length} ذكر',
                  icon: Icons.nights_stay,
                  color: const Color(0xFF3F51B5),
                  dhikrList: _eveningDhikr,
                ),
                const SizedBox(height: 15),
                _buildDhikrCategoryCard(
                  title: 'أذكار عامة',
                  subtitle: '${_generalDhikr.length} ذكر',
                  icon: Icons.auto_awesome,
                  color: const Color(0xFF4CAF50),
                  dhikrList: _generalDhikr,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDhikrCategoryCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<DhikrModel> dhikrList,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DhikrScreen(
              category: title,
              dhikrList: dhikrList,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                icon,
                color: color,
                size: 30,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.amiri(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.amiri(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
