class UserSettingsModel {
  final bool isDarkMode;
  final String language;
  final bool notificationsEnabled;
  final bool soundEnabled;
  final double fontSize;
  final String fontFamily;
  final bool vibrationEnabled;
  final bool autoPlayAudio;
  final int dhikrReminderInterval; // in minutes
  final bool smartReminders;
  final String location;
  final double latitude;
  final double longitude;
  final bool locationBasedPrayers;
  final Map<String, bool> prayerNotifications;
  final Map<String, int> prayerReminderMinutes;
  final bool hijriCalendar;
  final String calculationMethod;
  final int madhab; // 0 for Shafi, 1 for Hanafi
  final bool showTransliteration;
  final bool showTranslation;
  final bool showBenefits;
  final String preferredReciter;
  final double audioSpeed;
  final bool backgroundAudio;

  UserSettingsModel({
    this.isDarkMode = false,
    this.language = 'ar',
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.fontSize = 16.0,
    this.fontFamily = 'Amiri',
    this.vibrationEnabled = true,
    this.autoPlayAudio = false,
    this.dhikrReminderInterval = 60,
    this.smartReminders = true,
    this.location = '',
    this.latitude = 0.0,
    this.longitude = 0.0,
    this.locationBasedPrayers = true,
    this.prayerNotifications = const {
      'الفجر': true,
      'الظهر': true,
      'العصر': true,
      'المغرب': true,
      'العشاء': true,
    },
    this.prayerReminderMinutes = const {
      'الفجر': 10,
      'الظهر': 10,
      'العصر': 10,
      'المغرب': 10,
      'العشاء': 10,
    },
    this.hijriCalendar = true,
    this.calculationMethod = 'UmmAlQura',
    this.madhab = 0,
    this.showTransliteration = true,
    this.showTranslation = true,
    this.showBenefits = true,
    this.preferredReciter = 'default',
    this.audioSpeed = 1.0,
    this.backgroundAudio = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'isDarkMode': isDarkMode ? 1 : 0,
      'language': language,
      'notificationsEnabled': notificationsEnabled ? 1 : 0,
      'soundEnabled': soundEnabled ? 1 : 0,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'vibrationEnabled': vibrationEnabled ? 1 : 0,
      'autoPlayAudio': autoPlayAudio ? 1 : 0,
      'dhikrReminderInterval': dhikrReminderInterval,
      'smartReminders': smartReminders ? 1 : 0,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'locationBasedPrayers': locationBasedPrayers ? 1 : 0,
      'prayerNotifications': prayerNotifications.toString(),
      'prayerReminderMinutes': prayerReminderMinutes.toString(),
      'hijriCalendar': hijriCalendar ? 1 : 0,
      'calculationMethod': calculationMethod,
      'madhab': madhab,
      'showTransliteration': showTransliteration ? 1 : 0,
      'showTranslation': showTranslation ? 1 : 0,
      'showBenefits': showBenefits ? 1 : 0,
      'preferredReciter': preferredReciter,
      'audioSpeed': audioSpeed,
      'backgroundAudio': backgroundAudio ? 1 : 0,
    };
  }

  factory UserSettingsModel.fromMap(Map<String, dynamic> map) {
    return UserSettingsModel(
      isDarkMode: map['isDarkMode'] == 1,
      language: map['language'] ?? 'ar',
      notificationsEnabled: map['notificationsEnabled'] == 1,
      soundEnabled: map['soundEnabled'] == 1,
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      fontFamily: map['fontFamily'] ?? 'Amiri',
      vibrationEnabled: map['vibrationEnabled'] == 1,
      autoPlayAudio: map['autoPlayAudio'] == 1,
      dhikrReminderInterval: map['dhikrReminderInterval'] ?? 60,
      smartReminders: map['smartReminders'] == 1,
      location: map['location'] ?? '',
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      locationBasedPrayers: map['locationBasedPrayers'] == 1,
      hijriCalendar: map['hijriCalendar'] == 1,
      calculationMethod: map['calculationMethod'] ?? 'UmmAlQura',
      madhab: map['madhab'] ?? 0,
      showTransliteration: map['showTransliteration'] == 1,
      showTranslation: map['showTranslation'] == 1,
      showBenefits: map['showBenefits'] == 1,
      preferredReciter: map['preferredReciter'] ?? 'default',
      audioSpeed: map['audioSpeed']?.toDouble() ?? 1.0,
      backgroundAudio: map['backgroundAudio'] == 1,
    );
  }

  UserSettingsModel copyWith({
    bool? isDarkMode,
    String? language,
    bool? notificationsEnabled,
    bool? soundEnabled,
    double? fontSize,
    String? fontFamily,
    bool? vibrationEnabled,
    bool? autoPlayAudio,
    int? dhikrReminderInterval,
    bool? smartReminders,
    String? location,
    double? latitude,
    double? longitude,
    bool? locationBasedPrayers,
    Map<String, bool>? prayerNotifications,
    Map<String, int>? prayerReminderMinutes,
    bool? hijriCalendar,
    String? calculationMethod,
    int? madhab,
    bool? showTransliteration,
    bool? showTranslation,
    bool? showBenefits,
    String? preferredReciter,
    double? audioSpeed,
    bool? backgroundAudio,
  }) {
    return UserSettingsModel(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      autoPlayAudio: autoPlayAudio ?? this.autoPlayAudio,
      dhikrReminderInterval: dhikrReminderInterval ?? this.dhikrReminderInterval,
      smartReminders: smartReminders ?? this.smartReminders,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationBasedPrayers: locationBasedPrayers ?? this.locationBasedPrayers,
      prayerNotifications: prayerNotifications ?? this.prayerNotifications,
      prayerReminderMinutes: prayerReminderMinutes ?? this.prayerReminderMinutes,
      hijriCalendar: hijriCalendar ?? this.hijriCalendar,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      madhab: madhab ?? this.madhab,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      showTranslation: showTranslation ?? this.showTranslation,
      showBenefits: showBenefits ?? this.showBenefits,
      preferredReciter: preferredReciter ?? this.preferredReciter,
      audioSpeed: audioSpeed ?? this.audioSpeed,
      backgroundAudio: backgroundAudio ?? this.backgroundAudio,
    );
  }
}
