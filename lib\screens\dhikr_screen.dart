import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:animate_do/animate_do.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_tts/flutter_tts.dart';

import '../models/dhikr_model.dart';
import '../data/dhikr_database.dart';

class DhikrScreen extends StatefulWidget {
  final String category;
  final List<DhikrModel> dhikrList;

  const DhikrScreen({
    super.key,
    required this.category,
    required this.dhikrList,
  });

  @override
  State<DhikrScreen> createState() => _DhikrScreenState();
}

class _DhikrScreenState extends State<DhikrScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _counterController;
  late AnimationController _progressController;
  
  int _currentIndex = 0;
  int _currentCount = 0;
  bool _isPlaying = false;
  
  final FlutterTts _flutterTts = FlutterTts();
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _counterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _initializeTts();
  }

  void _initializeTts() async {
    await _flutterTts.setLanguage('ar');
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _counterController.dispose();
    _progressController.dispose();
    _flutterTts.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _incrementCounter() {
    if (_currentCount < widget.dhikrList[_currentIndex].count) {
      setState(() {
        _currentCount++;
      });
      
      _counterController.forward().then((_) {
        _counterController.reverse();
      });
      
      // Update progress
      _progressController.animateTo(
        _currentCount / widget.dhikrList[_currentIndex].count,
      );
      
      // Haptic feedback
      HapticFeedback.lightImpact();
      
      // Check if completed
      if (_currentCount >= widget.dhikrList[_currentIndex].count) {
        _showCompletionDialog();
      }
    }
  }

  void _resetCounter() {
    setState(() {
      _currentCount = 0;
    });
    _progressController.reset();
  }

  void _nextDhikr() {
    if (_currentIndex < widget.dhikrList.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousDhikr() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _playAudio() async {
    if (!_isPlaying) {
      setState(() {
        _isPlaying = true;
      });
      
      await _flutterTts.speak(widget.dhikrList[_currentIndex].arabicText);
      
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'مبارك!',
            style: GoogleFonts.amiri(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF4CAF50),
            ),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.check_circle,
                color: Color(0xFF4CAF50),
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                'لقد أكملت هذا الذكر بنجاح',
                style: GoogleFonts.amiri(
                  fontSize: 18,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetCounter();
              },
              child: Text(
                'إعادة',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _nextDhikr();
                _resetCounter();
              },
              child: Text(
                'التالي',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F8E9),
      appBar: AppBar(
        title: Text(
          widget.category,
          style: GoogleFonts.amiri(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _playAudio,
            icon: Icon(
              _isPlaying ? Icons.volume_up : Icons.volume_off,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                  _currentCount = 0;
                });
                _progressController.reset();
              },
              itemCount: widget.dhikrList.length,
              itemBuilder: (context, index) {
                return _buildDhikrCard(widget.dhikrList[index]);
              },
            ),
          ),
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_currentIndex + 1} من ${widget.dhikrList.length}',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '$_currentCount / ${widget.dhikrList[_currentIndex].count}',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedBuilder(
            animation: _progressController,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressController.value,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF4CAF50),
                ),
                minHeight: 6,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDhikrCard(DhikrModel dhikr) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.green.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              dhikr.arabicText,
              style: GoogleFonts.amiri(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2E7D32),
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            if (dhikr.transliteration.isNotEmpty) ...[
              const SizedBox(height: 20),
              Text(
                dhikr.transliteration,
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (dhikr.translation.isNotEmpty) ...[
              const SizedBox(height: 15),
              Text(
                dhikr.translation,
                style: GoogleFonts.amiri(
                  fontSize: 18,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (dhikr.benefits != null && dhikr.benefits!.isNotEmpty) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  'الفائدة: ${dhikr.benefits}',
                  style: GoogleFonts.amiri(
                    fontSize: 14,
                    color: const Color(0xFF2E7D32),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: _currentIndex > 0 ? _previousDhikr : null,
            icon: const Icon(Icons.arrow_back_ios),
            iconSize: 30,
            color: _currentIndex > 0 ? const Color(0xFF4CAF50) : Colors.grey,
          ),
          ScaleTransition(
            scale: Tween<double>(begin: 1.0, end: 1.2).animate(
              CurvedAnimation(
                parent: _counterController,
                curve: Curves.elasticOut,
              ),
            ),
            child: GestureDetector(
              onTap: _incrementCounter,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: _currentIndex < widget.dhikrList.length - 1 ? _nextDhikr : null,
            icon: const Icon(Icons.arrow_forward_ios),
            iconSize: 30,
            color: _currentIndex < widget.dhikrList.length - 1 
                ? const Color(0xFF4CAF50) 
                : Colors.grey,
          ),
        ],
      ),
    );
  }
}
