import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:animate_do/animate_do.dart';
import 'dart:async';
import 'dart:math' as math;
import '../services/tts_muezzin_service.dart';

class IslamicHomeScreen extends StatefulWidget {
  const IslamicHomeScreen({super.key});

  @override
  State<IslamicHomeScreen> createState() => _IslamicHomeScreenState();
}

class _IslamicHomeScreenState extends State<IslamicHomeScreen>
    with TickerProviderStateMixin {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();
  late AnimationController _clockController;
  late AnimationController _breathingController;
  
  // Mock prayer times
  final Map<String, DateTime> _prayerTimes = {
    'الفجر': DateTime.now().copyWith(hour: 5, minute: 30),
    'الشروق': DateTime.now().copyWith(hour: 6, minute: 45),
    'الظهر': DateTime.now().copyWith(hour: 12, minute: 15),
    'العصر': DateTime.now().copyWith(hour: 15, minute: 30),
    'المغرب': DateTime.now().copyWith(hour: 18, minute: 45),
    'العشاء': DateTime.now().copyWith(hour: 20, minute: 15),
  };

  @override
  void initState() {
    super.initState();
    _clockController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();
    
    _breathingController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat(reverse: true);
    
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _clockController.dispose();
    _breathingController.dispose();
    super.dispose();
  }

  Future<void> _playAdhan(String prayerName) async {
    final isFajr = prayerName == 'الفجر';

    try {
      await TTSMuezzinService.playAdhanTTS(isFajr: isFajr);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تشغيل أذان $prayerName',
              style: GoogleFonts.amiri(fontSize: 16),
            ),
            backgroundColor: const Color(0xFF2E7D32),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تشغيل الأذان: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في تشغيل الأذان',
              style: GoogleFonts.amiri(fontSize: 16),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  String _getGreeting() {
    final hour = _currentTime.hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'نهارك مبارك';
    } else {
      return 'مساء الخير';
    }
  }

  String _getNextPrayer() {
    final now = DateTime.now();
    for (var entry in _prayerTimes.entries) {
      if (now.isBefore(entry.value)) {
        return entry.key;
      }
    }
    return 'الفجر';
  }

  Duration _getTimeUntilNextPrayer() {
    final now = DateTime.now();
    final nextPrayer = _getNextPrayer();
    var nextPrayerTime = _prayerTimes[nextPrayer]!;
    
    if (now.isAfter(nextPrayerTime)) {
      nextPrayerTime = nextPrayerTime.add(const Duration(days: 1));
    }
    
    return nextPrayerTime.difference(now);
  }

  @override
  Widget build(BuildContext context) {
    final nextPrayer = _getNextPrayer();
    final timeUntilNext = _getTimeUntilNextPrayer();
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF388E3C),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildHeader(),
                const SizedBox(height: 30),
                _buildIslamicClock(),
                const SizedBox(height: 30),
                _buildNextPrayerCard(nextPrayer, timeUntilNext),
                const SizedBox(height: 30),
                _buildTodaysPrayerTimes(),
                const SizedBox(height: 30),
                _buildQuickDhikr(),
                const SizedBox(height: 30),
                _buildIslamicQuote(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeInDown(
      duration: const Duration(milliseconds: 800),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getGreeting(),
                style: GoogleFonts.amiri(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'بارك الله في يومك',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Icon(
              Icons.notifications_outlined,
              color: Colors.white,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIslamicClock() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1000),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            // Islamic decoration
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIslamicPattern(),
                const SizedBox(width: 20),
                const Icon(
                  Icons.mosque,
                  color: Colors.white,
                  size: 30,
                ),
                const SizedBox(width: 20),
                _buildIslamicPattern(),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Digital Clock
            AnimatedBuilder(
              animation: _clockController,
              builder: (context, child) {
                return Text(
                  DateFormat('HH:mm:ss').format(_currentTime),
                  style: GoogleFonts.orbitron(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 3,
                  ),
                );
              },
            ),
            
            const SizedBox(height: 15),
            
            // Gregorian Date
            Text(
              DateFormat('EEEE، d MMMM yyyy', 'ar').format(_currentTime),
              style: GoogleFonts.amiri(
                fontSize: 18,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Hijri Date (approximate)
            Text(
              '20 ذو الحجة 1446 هـ',
              style: GoogleFonts.amiri(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIslamicPattern() {
    return AnimatedBuilder(
      animation: _breathingController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_breathingController.value * 0.1),
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.star,
              color: Colors.white,
              size: 16,
            ),
          ),
        );
      },
    );
  }

  Widget _buildNextPrayerCard(String nextPrayer, Duration timeUntilNext) {
    return FadeInLeft(
      duration: const Duration(milliseconds: 1200),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(25),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.access_time,
                    color: Color(0xFF2E7D32),
                    size: 30,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الصلاة القادمة',
                        style: GoogleFonts.amiri(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        nextPrayer,
                        style: GoogleFonts.amiri(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          DateFormat('HH:mm').format(_prayerTimes[nextPrayer]!),
                          style: GoogleFonts.orbitron(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(width: 10),
                        GestureDetector(
                          onTap: () => _playAdhan(nextPrayer),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.volume_up,
                              color: Color(0xFF2E7D32),
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      'متبقي: ${timeUntilNext.inHours.toString().padLeft(2, '0')}:${(timeUntilNext.inMinutes % 60).toString().padLeft(2, '0')}',
                      style: GoogleFonts.amiri(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaysPrayerTimes() {
    return FadeInRight(
      duration: const Duration(milliseconds: 1400),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أوقات الصلاة اليوم',
              style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2E7D32),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPrayerTimeItem('الفجر', _prayerTimes['الفجر']!),
                _buildPrayerTimeItem('الظهر', _prayerTimes['الظهر']!),
                _buildPrayerTimeItem('العصر', _prayerTimes['العصر']!),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPrayerTimeItem('المغرب', _prayerTimes['المغرب']!),
                _buildPrayerTimeItem('العشاء', _prayerTimes['العشاء']!),
                const SizedBox(width: 60), // Placeholder for alignment
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimeItem(String prayer, DateTime time) {
    return GestureDetector(
      onTap: () => _playAdhan(prayer),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF2E7D32).withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  prayer,
                  style: GoogleFonts.amiri(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(
                  Icons.volume_up,
                  size: 12,
                  color: Color(0xFF2E7D32),
                ),
              ],
            ),
            const SizedBox(height: 5),
            Text(
              DateFormat('HH:mm').format(time),
              style: GoogleFonts.orbitron(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickDhikr() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1600),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(25),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4CAF50),
              Color(0xFF2E7D32),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            Text(
              'ذكر سريع',
              style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
              style: GoogleFonts.amiri(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 15),
            Text(
              'سبحان الله وبحمده',
              style: GoogleFonts.amiri(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF2E7D32),
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                'المزيد من الأذكار',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIslamicQuote() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1800),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(25),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            const Icon(
              Icons.format_quote,
              color: Colors.white,
              size: 30,
            ),
            const SizedBox(height: 15),
            Text(
              '"وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا"',
              style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              'الطلاق: 2',
              style: GoogleFonts.amiri(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
