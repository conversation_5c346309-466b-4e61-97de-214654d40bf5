import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AudioService {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static final FlutterTts _flutterTts = FlutterTts();
  static bool _isInitialized = false;
  static bool _isSpeaking = false;

  /// تهيئة خدمة الصوت
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة TTS
      await _initializeTts();
      
      // تهيئة مشغل الصوت
      await _initializeAudioPlayer();
      
      _isInitialized = true;
      debugPrint('تم تهيئة خدمة الصوت بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الصوت: $e');
    }
  }

  static Future<void> _initializeTts() async {
    // تعيين اللغة العربية
    await _flutterTts.setLanguage('ar');
    
    // تعيين الإعدادات الافتراضية
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    // معالجات الأحداث
    _flutterTts.setStartHandler(() {
      _isSpeaking = true;
      debugPrint('بدء التشغيل الصوتي');
    });

    _flutterTts.setCompletionHandler(() {
      _isSpeaking = false;
      debugPrint('انتهاء التشغيل الصوتي');
    });

    _flutterTts.setErrorHandler((message) {
      _isSpeaking = false;
      debugPrint('خطأ في التشغيل الصوتي: $message');
    });

    // تحميل الإعدادات المحفوظة
    await _loadAudioSettings();
  }

  static Future<void> _initializeAudioPlayer() async {
    // معالج انتهاء التشغيل
    _audioPlayer.onPlayerComplete.listen((event) {
      debugPrint('انتهى تشغيل الملف الصوتي');
    });

    // معالج الأخطاء
    _audioPlayer.onPlayerStateChanged.listen((state) {
      debugPrint('حالة المشغل: $state');
    });
  }

  /// تحميل إعدادات الصوت المحفوظة
  static Future<void> _loadAudioSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    final speechRate = prefs.getDouble('speech_rate') ?? 0.5;
    final volume = prefs.getDouble('volume') ?? 1.0;
    final pitch = prefs.getDouble('pitch') ?? 1.0;

    await _flutterTts.setSpeechRate(speechRate);
    await _flutterTts.setVolume(volume);
    await _flutterTts.setPitch(pitch);
  }

  /// حفظ إعدادات الصوت
  static Future<void> saveAudioSettings({
    double? speechRate,
    double? volume,
    double? pitch,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    if (speechRate != null) {
      await prefs.setDouble('speech_rate', speechRate);
      await _flutterTts.setSpeechRate(speechRate);
    }

    if (volume != null) {
      await prefs.setDouble('volume', volume);
      await _flutterTts.setVolume(volume);
    }

    if (pitch != null) {
      await prefs.setDouble('pitch', pitch);
      await _flutterTts.setPitch(pitch);
    }
  }

  /// تشغيل النص بالصوت
  static Future<void> speakText(String text) async {
    if (!_isInitialized) await initialize();

    try {
      // إيقاف أي تشغيل حالي
      await stopSpeaking();
      
      // بدء التشغيل الجديد
      await _flutterTts.speak(text);
    } catch (e) {
      debugPrint('خطأ في تشغيل النص: $e');
    }
  }

  /// إيقاف التشغيل الصوتي
  static Future<void> stopSpeaking() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
    }
  }

  /// إيقاف مؤقت للتشغيل
  static Future<void> pauseSpeaking() async {
    if (_isSpeaking) {
      await _flutterTts.pause();
    }
  }

  /// استئناف التشغيل
  static Future<void> resumeSpeaking() async {
    // ملاحظة: FlutterTts لا يدعم الاستئناف بشكل مباشر
    // يمكن تنفيذ هذا بطريقة أخرى إذا لزم الأمر
  }

  /// تشغيل ملف صوتي من الأصول
  static Future<void> playAudioFile(String assetPath) async {
    try {
      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(assetPath));
    } catch (e) {
      debugPrint('خطأ في تشغيل الملف الصوتي: $e');
    }
  }

  /// تشغيل صوت التسبيح
  static Future<void> playTasbihSound() async {
    // يمكن إضافة ملف صوتي للتسبيح في assets/sounds/
    // await playAudioFile('sounds/tasbih.mp3');
    
    // أو استخدام TTS لنطق "سبحان الله"
    await speakText('سبحان الله');
  }

  /// تشغيل صوت التحميد
  static Future<void> playTahmidSound() async {
    await speakText('الحمد لله');
  }

  /// تشغيل صوت التكبير
  static Future<void> playTakbirSound() async {
    await speakText('الله أكبر');
  }

  /// تشغيل صوت التهليل
  static Future<void> playTahlilSound() async {
    await speakText('لا إله إلا الله');
  }

  /// الحصول على قائمة الأصوات المتاحة
  static Future<List<dynamic>> getAvailableVoices() async {
    if (!_isInitialized) await initialize();
    
    try {
      final voices = await _flutterTts.getVoices;
      // تصفية الأصوات العربية فقط
      return voices.where((voice) => 
        voice['locale'].toString().startsWith('ar')).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الأصوات: $e');
      return [];
    }
  }

  /// تعيين الصوت المحدد
  static Future<void> setVoice(Map<String, String> voice) async {
    try {
      await _flutterTts.setVoice(voice);
      
      // حفظ الصوت المختار
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_voice', voice.toString());
    } catch (e) {
      debugPrint('خطأ في تعيين الصوت: $e');
    }
  }

  /// تشغيل تأثير صوتي للإنجاز
  static Future<void> playCompletionSound() async {
    // يمكن إضافة ملف صوتي للإنجاز
    // await playAudioFile('sounds/completion.mp3');
    
    // أو استخدام TTS
    await speakText('مبارك! لقد أكملت الذكر');
  }

  /// تشغيل صوت التنبيه
  static Future<void> playNotificationSound() async {
    // يمكن إضافة ملف صوتي للتنبيه
    // await playAudioFile('sounds/notification.mp3');
  }

  /// فحص حالة التشغيل
  static bool get isSpeaking => _isSpeaking;

  /// فحص حالة التهيئة
  static bool get isInitialized => _isInitialized;

  /// الحصول على إعدادات الصوت الحالية
  static Future<Map<String, double>> getCurrentAudioSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    return {
      'speechRate': prefs.getDouble('speech_rate') ?? 0.5,
      'volume': prefs.getDouble('volume') ?? 1.0,
      'pitch': prefs.getDouble('pitch') ?? 1.0,
    };
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    await stopSpeaking();
    await _audioPlayer.dispose();
    _isInitialized = false;
  }

  /// تشغيل ذكر كامل مع فواصل
  static Future<void> playDhikrWithPauses(
    String dhikrText, 
    int repetitions,
    {Duration pauseBetween = const Duration(seconds: 2)}
  ) async {
    for (int i = 0; i < repetitions; i++) {
      await speakText(dhikrText);
      
      // انتظار انتهاء التشغيل
      while (_isSpeaking) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      // فاصل بين التكرارات
      if (i < repetitions - 1) {
        await Future.delayed(pauseBetween);
      }
    }
  }

  /// تشغيل قائمة أذكار متتالية
  static Future<void> playDhikrSequence(List<String> dhikrList) async {
    for (int i = 0; i < dhikrList.length; i++) {
      await speakText(dhikrList[i]);
      
      // انتظار انتهاء التشغيل
      while (_isSpeaking) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      // فاصل بين الأذكار
      if (i < dhikrList.length - 1) {
        await Future.delayed(const Duration(seconds: 3));
      }
    }
  }
}
