import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SmartReminderService {
  static const String _lastDhikrTimeKey = 'last_dhikr_time';
  static const String _dhikrCountKey = 'dhikr_count_today';
  static const String _userPatternKey = 'user_pattern';
  
  static Timer? _reminderTimer;
  static final Random _random = Random();

  /// تحليل نمط المستخدم وإرسال تذكيرات ذكية
  static Future<void> initializeSmartReminders() async {
    final prefs = await SharedPreferences.getInstance();
    final lastDhikrTime = prefs.getInt(_lastDhikrTimeKey) ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    
    // إذا مر أكثر من ساعة على آخر ذكر، ابدأ التذكيرات
    if (currentTime - lastDhikrTime > 3600000) { // ساعة واحدة
      _startSmartReminders();
    }
  }

  static void _startSmartReminders() {
    _reminderTimer?.cancel();
    
    // تذكير كل 30-90 دقيقة بشكل عشوائي
    final nextReminderMinutes = 30 + _random.nextInt(60);
    
    _reminderTimer = Timer(Duration(minutes: nextReminderMinutes), () {
      _sendSmartReminder();
      _startSmartReminders(); // جدولة التذكير التالي
    });
  }

  static Future<void> _sendSmartReminder() async {
    final reminderMessage = _getPersonalizedReminderMessage();
    
    // هنا يمكن إضافة إشعار محلي
    debugPrint('تذكير ذكي: $reminderMessage');
    
    // يمكن إضافة إشعار فعلي هنا
    // await _showLocalNotification(reminderMessage);
  }

  static String _getPersonalizedReminderMessage() {
    final messages = [
      'حان وقت الذكر والتسبيح 🤲',
      'لا تنس ذكر الله في هذا الوقت المبارك ✨',
      'استغل هذه اللحظة في التسبيح والحمد 🌟',
      'وقت مثالي لقراءة أذكار الصباح/المساء 🌅',
      'ذكر الله يطمئن القلوب 💚',
      'لحظة من السكينة مع الأذكار 🕊️',
      'استثمر دقائق في الذكر والدعاء 🤲',
    ];
    
    final hour = DateTime.now().hour;
    
    // تخصيص الرسالة حسب الوقت
    if (hour >= 6 && hour < 12) {
      return 'صباح مبارك! ${messages[_random.nextInt(messages.length)]}';
    } else if (hour >= 12 && hour < 18) {
      return 'نهارك مبارك! ${messages[_random.nextInt(messages.length)]}';
    } else {
      return 'مساء الخير! ${messages[_random.nextInt(messages.length)]}';
    }
  }

  /// تسجيل استخدام الذكر لتحليل النمط
  static Future<void> recordDhikrUsage(String category) async {
    final prefs = await SharedPreferences.getInstance();
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    
    // تحديث آخر وقت للذكر
    await prefs.setInt(_lastDhikrTimeKey, currentTime);
    
    // زيادة عداد الأذكار اليوم
    final today = DateTime.now().day;
    final lastRecordedDay = prefs.getInt('last_recorded_day') ?? 0;
    
    if (today != lastRecordedDay) {
      // يوم جديد، إعادة تعيين العداد
      await prefs.setInt(_dhikrCountKey, 1);
      await prefs.setInt('last_recorded_day', today);
    } else {
      final currentCount = prefs.getInt(_dhikrCountKey) ?? 0;
      await prefs.setInt(_dhikrCountKey, currentCount + 1);
    }
    
    // تحليل النمط وحفظه
    await _analyzeAndSavePattern(category, currentTime);
  }

  static Future<void> _analyzeAndSavePattern(String category, int timestamp) async {
    final prefs = await SharedPreferences.getInstance();
    final hour = DateTime.fromMillisecondsSinceEpoch(timestamp).hour;
    
    // حفظ نمط الاستخدام (الساعة المفضلة لكل فئة)
    final patternKey = '${category}_preferred_hours';
    final currentPattern = prefs.getStringList(patternKey) ?? [];
    currentPattern.add(hour.toString());
    
    // الاحتفاظ بآخر 50 استخدام فقط
    if (currentPattern.length > 50) {
      currentPattern.removeAt(0);
    }
    
    await prefs.setStringList(patternKey, currentPattern);
  }

  /// الحصول على اقتراحات ذكية للأذكار
  static Future<List<String>> getSmartSuggestions() async {
    final prefs = await SharedPreferences.getInstance();
    final hour = DateTime.now().hour;
    final suggestions = <String>[];

    // اقتراحات حسب الوقت
    if (hour >= 6 && hour < 12) {
      suggestions.addAll(['أذكار الصباح', 'الاستغفار', 'التسبيح']);
    } else if (hour >= 17 && hour < 21) {
      suggestions.addAll(['أذكار المساء', 'أذكار بعد الصلاة']);
    } else if (hour >= 21 || hour < 6) {
      suggestions.addAll(['أذكار النوم', 'الاستغفار']);
    } else {
      suggestions.addAll(['أذكار عامة', 'التسبيح', 'التحميد']);
    }

    // إضافة اقتراحات بناءً على النمط السابق
    final todayCount = prefs.getInt(_dhikrCountKey) ?? 0;
    if (todayCount < 3) {
      suggestions.insert(0, 'ابدأ يومك بالذكر');
    }

    return suggestions.take(3).toList();
  }

  /// الحصول على إحصائيات الاستخدام
  static Future<Map<String, dynamic>> getUsageStats() async {
    final prefs = await SharedPreferences.getInstance();
    final todayCount = prefs.getInt(_dhikrCountKey) ?? 0;
    final lastDhikrTime = prefs.getInt(_lastDhikrTimeKey) ?? 0;
    
    final timeSinceLastDhikr = lastDhikrTime > 0 
        ? DateTime.now().millisecondsSinceEpoch - lastDhikrTime
        : 0;

    return {
      'todayCount': todayCount,
      'timeSinceLastDhikr': timeSinceLastDhikr,
      'lastDhikrTime': lastDhikrTime,
    };
  }

  /// تنظيف الموارد
  static void dispose() {
    _reminderTimer?.cancel();
  }

  /// تحديد أفضل وقت للتذكير بناءً على نمط المستخدم
  static Future<List<int>> getOptimalReminderTimes() async {
    final prefs = await SharedPreferences.getInstance();
    final categories = ['أذكار الصباح', 'أذكار المساء', 'أذكار عامة'];
    final optimalTimes = <int>[];

    for (final category in categories) {
      final patternKey = '${category}_preferred_hours';
      final pattern = prefs.getStringList(patternKey) ?? [];
      
      if (pattern.isNotEmpty) {
        // حساب الساعة الأكثر استخداماً
        final hourCounts = <int, int>{};
        for (final hourStr in pattern) {
          final hour = int.tryParse(hourStr) ?? 0;
          hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
        }
        
        final mostUsedHour = hourCounts.entries
            .reduce((a, b) => a.value > b.value ? a : b)
            .key;
        
        if (!optimalTimes.contains(mostUsedHour)) {
          optimalTimes.add(mostUsedHour);
        }
      }
    }

    return optimalTimes;
  }

  /// تقييم مستوى التفاعل مع التطبيق
  static Future<String> getUserEngagementLevel() async {
    final stats = await getUsageStats();
    final todayCount = stats['todayCount'] as int;
    
    if (todayCount >= 10) {
      return 'ممتاز - مستخدم نشط جداً 🌟';
    } else if (todayCount >= 5) {
      return 'جيد جداً - مستخدم نشط 👍';
    } else if (todayCount >= 2) {
      return 'جيد - استمر في التقدم 📈';
    } else if (todayCount >= 1) {
      return 'بداية جيدة - يمكنك المزيد 🌱';
    } else {
      return 'ابدأ رحلتك مع الأذكار اليوم 🚀';
    }
  }
}
