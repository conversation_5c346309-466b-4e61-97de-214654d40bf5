import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/dhikr_model.dart';
import '../models/user_settings_model.dart';
import '../models/prayer_time_model.dart';

class DhikrDatabase {
  static Database? _database;
  static const String _databaseName = 'dhikr_database.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _dhikrTable = 'dhikr';
  static const String _settingsTable = 'settings';
  static const String _prayerTimesTable = 'prayer_times';
  static const String _userProgressTable = 'user_progress';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create dhikr table
    await db.execute('''
      CREATE TABLE $_dhikrTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        arabicText TEXT NOT NULL,
        transliteration TEXT NOT NULL,
        translation TEXT NOT NULL,
        category TEXT NOT NULL,
        count INTEGER DEFAULT 1,
        reference TEXT,
        benefits TEXT,
        isFavorite INTEGER DEFAULT 0,
        lastUsed INTEGER,
        timesUsed INTEGER DEFAULT 0
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE $_settingsTable (
        id INTEGER PRIMARY KEY,
        isDarkMode INTEGER DEFAULT 0,
        language TEXT DEFAULT 'ar',
        notificationsEnabled INTEGER DEFAULT 1,
        soundEnabled INTEGER DEFAULT 1,
        fontSize REAL DEFAULT 16.0,
        fontFamily TEXT DEFAULT 'Amiri',
        vibrationEnabled INTEGER DEFAULT 1,
        autoPlayAudio INTEGER DEFAULT 0,
        dhikrReminderInterval INTEGER DEFAULT 60,
        smartReminders INTEGER DEFAULT 1,
        location TEXT DEFAULT '',
        latitude REAL DEFAULT 0.0,
        longitude REAL DEFAULT 0.0,
        locationBasedPrayers INTEGER DEFAULT 1,
        prayerNotifications TEXT DEFAULT '',
        prayerReminderMinutes TEXT DEFAULT '',
        hijriCalendar INTEGER DEFAULT 1,
        calculationMethod TEXT DEFAULT 'UmmAlQura',
        madhab INTEGER DEFAULT 0,
        showTransliteration INTEGER DEFAULT 1,
        showTranslation INTEGER DEFAULT 1,
        showBenefits INTEGER DEFAULT 1,
        preferredReciter TEXT DEFAULT 'default',
        audioSpeed REAL DEFAULT 1.0,
        backgroundAudio INTEGER DEFAULT 0
      )
    ''');

    // Create prayer times table
    await db.execute('''
      CREATE TABLE $_prayerTimesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fajr INTEGER NOT NULL,
        sunrise INTEGER NOT NULL,
        dhuhr INTEGER NOT NULL,
        asr INTEGER NOT NULL,
        maghrib INTEGER NOT NULL,
        isha INTEGER NOT NULL,
        date INTEGER NOT NULL,
        location TEXT NOT NULL
      )
    ''');

    // Create user progress table
    await db.execute('''
      CREATE TABLE $_userProgressTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        dhikrId INTEGER NOT NULL,
        currentCount INTEGER DEFAULT 0,
        targetCount INTEGER DEFAULT 1,
        date TEXT NOT NULL,
        completed INTEGER DEFAULT 0,
        FOREIGN KEY (dhikrId) REFERENCES $_dhikrTable (id)
      )
    ''');

    // Insert default dhikr data
    await _insertDefaultDhikr(db);
  }

  static Future<void> _insertDefaultDhikr(Database db) async {
    final List<Map<String, dynamic>> defaultDhikr = [
      // أذكار الصباح
      {
        'arabicText': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ وَلَا يَئُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ',
        'transliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim. Allahu la ilaha illa huwa\'l-hayyu\'l-qayyum...',
        'translation': 'أعوذ بالله من الشيطان الرجيم. الله لا إله إلا هو الحي القيوم...',
        'category': 'أذكار الصباح',
        'count': 1,
        'reference': 'آية الكرسي - البقرة 255',
        'benefits': 'حفظ من الشياطين طوال اليوم'
      },
      {
        'arabicText': 'بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ',
        'transliteration': 'Bismillahi\'lladhi la yadurru ma\'a ismihi shay\'un fi\'l-ardi wa la fi\'s-sama\'i wa huwa\'s-sami\'u\'l-\'alim',
        'translation': 'بسم الله الذي لا يضر مع اسمه شيء في الأرض ولا في السماء وهو السميع العليم',
        'category': 'أذكار الصباح',
        'count': 3,
        'reference': 'أبو داود والترمذي',
        'benefits': 'حماية من كل ضرر'
      },
      {
        'arabicText': 'رَضِيتُ بِاللَّهِ رَبًّا، وَبِالْإِسْلَامِ دِينًا، وَبِمُحَمَّدٍ صَلَّى اللَّهُ عَلَيْهِ وَسَلَّمَ رَسُولًا',
        'transliteration': 'Raditu billahi rabban, wa bil-islami dinan, wa bi Muhammadin rasolan',
        'translation': 'رضيت بالله رباً، وبالإسلام ديناً، وبمحمد صلى الله عليه وسلم رسولاً',
        'category': 'أذكار الصباح',
        'count': 3,
        'reference': 'أبو داود',
        'benefits': 'رضا الله ودخول الجنة'
      },
      // أذكار المساء
      {
        'arabicText': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        'transliteration': 'Amsayna wa amsa\'l-mulku lillah, wal-hamdu lillah, la ilaha illa\'llahu wahdahu la sharika lah...',
        'translation': 'أمسينا وأمسى الملك لله، والحمد لله، لا إله إلا الله وحده لا شريك له...',
        'category': 'أذكار المساء',
        'count': 1,
        'reference': 'مسلم',
        'benefits': 'تسليم الأمر لله والاعتراف بوحدانيته'
      },
      // أذكار بعد الصلاة
      {
        'arabicText': 'سُبْحَانَ اللَّهِ',
        'transliteration': 'Subhan Allah',
        'translation': 'سبحان الله',
        'category': 'أذكار بعد الصلاة',
        'count': 33,
        'reference': 'البخاري ومسلم',
        'benefits': 'تنزيه الله عن كل نقص'
      },
      {
        'arabicText': 'الْحَمْدُ لِلَّهِ',
        'transliteration': 'Alhamdu lillah',
        'translation': 'الحمد لله',
        'category': 'أذكار بعد الصلاة',
        'count': 33,
        'reference': 'البخاري ومسلم',
        'benefits': 'حمد الله على نعمه'
      },
      {
        'arabicText': 'اللَّهُ أَكْبَرُ',
        'transliteration': 'Allahu akbar',
        'translation': 'الله أكبر',
        'category': 'أذكار بعد الصلاة',
        'count': 34,
        'reference': 'البخاري ومسلم',
        'benefits': 'تعظيم الله'
      },
      // أذكار النوم
      {
        'arabicText': 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
        'transliteration': 'Bismika Allahumma amutu wa ahya',
        'translation': 'باسمك اللهم أموت وأحيا',
        'category': 'أذكار النوم',
        'count': 1,
        'reference': 'البخاري',
        'benefits': 'تسليم الأمر لله عند النوم'
      },
      // الاستغفار
      {
        'arabicText': 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيَّ الْقَيُّومَ وَأَتُوبُ إِلَيْهِ',
        'transliteration': 'Astaghfiru\'llaha\'l-\'azima\'lladhi la ilaha illa huwa\'l-hayya\'l-qayyuma wa atubu ilayh',
        'translation': 'أستغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه',
        'category': 'الاستغفار',
        'count': 1,
        'reference': 'أبو داود والترمذي',
        'benefits': 'مغفرة الذنوب'
      },
      {
        'arabicText': 'رَبِّ اغْفِرْ لِي ذَنْبِي وَخَطَئِي وَجَهْلِي',
        'transliteration': 'Rabbi\'ghfir li dhanbi wa khata\'i wa jahli',
        'translation': 'رب اغفر لي ذنبي وخطئي وجهلي',
        'category': 'الاستغفار',
        'count': 1,
        'reference': 'البخاري ومسلم',
        'benefits': 'طلب المغفرة الشاملة'
      },
      // أذكار عامة
      {
        'arabicText': 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        'transliteration': 'La ilaha illa\'llahu wahdahu la sharika lah, lahu\'l-mulku wa lahu\'l-hamdu wa huwa \'ala kulli shay\'in qadir',
        'translation': 'لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير',
        'category': 'أذكار عامة',
        'count': 10,
        'reference': 'البخاري ومسلم',
        'benefits': 'أجر عتق عشر رقاب'
      },
      {
        'arabicText': 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
        'transliteration': 'Subhan Allahi wa bihamdih',
        'translation': 'سبحان الله وبحمده',
        'category': 'التسبيح',
        'count': 100,
        'reference': 'البخاري ومسلم',
        'benefits': 'حط الخطايا وإن كانت مثل زبد البحر'
      },
      {
        'arabicText': 'سُبْحَانَ اللَّهِ الْعَظِيمِ وَبِحَمْدِهِ',
        'transliteration': 'Subhan Allahi\'l-\'azimi wa bihamdih',
        'translation': 'سبحان الله العظيم وبحمده',
        'category': 'التسبيح',
        'count': 1,
        'reference': 'البخاري ومسلم',
        'benefits': 'ثقيلة في الميزان، حبيبة إلى الرحمن'
      },
    ];

    for (var dhikr in defaultDhikr) {
      await db.insert(_dhikrTable, dhikr);
    }
  }

  // CRUD operations for Dhikr
  static Future<int> insertDhikr(DhikrModel dhikr) async {
    final db = await database;
    return await db.insert(_dhikrTable, dhikr.toMap());
  }

  static Future<List<DhikrModel>> getAllDhikr() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_dhikrTable);
    return List.generate(maps.length, (i) => DhikrModel.fromMap(maps[i]));
  }

  static Future<List<DhikrModel>> getDhikrByCategory(String category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _dhikrTable,
      where: 'category = ?',
      whereArgs: [category],
    );
    return List.generate(maps.length, (i) => DhikrModel.fromMap(maps[i]));
  }

  static Future<List<DhikrModel>> getFavoriteDhikr() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _dhikrTable,
      where: 'isFavorite = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) => DhikrModel.fromMap(maps[i]));
  }

  static Future<int> updateDhikr(DhikrModel dhikr) async {
    final db = await database;
    return await db.update(
      _dhikrTable,
      dhikr.toMap(),
      where: 'id = ?',
      whereArgs: [dhikr.id],
    );
  }

  static Future<int> deleteDhikr(int id) async {
    final db = await database;
    return await db.delete(
      _dhikrTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  static Future<int> saveSettings(UserSettingsModel settings) async {
    final db = await database;
    return await db.insert(
      _settingsTable,
      settings.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<UserSettingsModel?> getSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_settingsTable);
    if (maps.isNotEmpty) {
      return UserSettingsModel.fromMap(maps.first);
    }
    return null;
  }

  // Prayer times operations
  static Future<int> savePrayerTimes(PrayerTimeModel prayerTimes) async {
    final db = await database;
    return await db.insert(_prayerTimesTable, prayerTimes.toMap());
  }

  static Future<PrayerTimeModel?> getPrayerTimesForDate(DateTime date) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _prayerTimesTable,
      where: 'date = ?',
      whereArgs: [date.millisecondsSinceEpoch],
    );
    if (maps.isNotEmpty) {
      return PrayerTimeModel.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
