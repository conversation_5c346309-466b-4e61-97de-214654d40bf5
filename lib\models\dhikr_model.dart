class DhikrModel {
  final int? id;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String category;
  final int count;
  final String? reference;
  final String? benefits;
  final bool isFavorite;
  final DateTime? lastUsed;
  final int timesUsed;

  DhikrModel({
    this.id,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    this.count = 1,
    this.reference,
    this.benefits,
    this.isFavorite = false,
    this.lastUsed,
    this.timesUsed = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'category': category,
      'count': count,
      'reference': reference,
      'benefits': benefits,
      'isFavorite': isFavorite ? 1 : 0,
      'lastUsed': lastUsed?.millisecondsSinceEpoch,
      'timesUsed': timesUsed,
    };
  }

  factory DhikrModel.fromMap(Map<String, dynamic> map) {
    return DhikrModel(
      id: map['id'],
      arabicText: map['arabicText'] ?? '',
      transliteration: map['transliteration'] ?? '',
      translation: map['translation'] ?? '',
      category: map['category'] ?? '',
      count: map['count'] ?? 1,
      reference: map['reference'],
      benefits: map['benefits'],
      isFavorite: map['isFavorite'] == 1,
      lastUsed: map['lastUsed'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['lastUsed'])
          : null,
      timesUsed: map['timesUsed'] ?? 0,
    );
  }

  DhikrModel copyWith({
    int? id,
    String? arabicText,
    String? transliteration,
    String? translation,
    String? category,
    int? count,
    String? reference,
    String? benefits,
    bool? isFavorite,
    DateTime? lastUsed,
    int? timesUsed,
  }) {
    return DhikrModel(
      id: id ?? this.id,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      category: category ?? this.category,
      count: count ?? this.count,
      reference: reference ?? this.reference,
      benefits: benefits ?? this.benefits,
      isFavorite: isFavorite ?? this.isFavorite,
      lastUsed: lastUsed ?? this.lastUsed,
      timesUsed: timesUsed ?? this.timesUsed,
    );
  }
}

class DhikrCategory {
  static const String morning = 'أذكار الصباح';
  static const String evening = 'أذكار المساء';
  static const String afterPrayer = 'أذكار بعد الصلاة';
  static const String beforeSleep = 'أذكار النوم';
  static const String wakeUp = 'أذكار الاستيقاظ';
  static const String general = 'أذكار عامة';
  static const String quran = 'آيات قرآنية';
  static const String prophetic = 'أدعية نبوية';
  static const String istighfar = 'الاستغفار';
  static const String tasbih = 'التسبيح';
  static const String tahmid = 'التحميد';
  static const String takbir = 'التكبير';
  static const String tahlil = 'التهليل';
  
  static List<String> getAllCategories() {
    return [
      morning,
      evening,
      afterPrayer,
      beforeSleep,
      wakeUp,
      general,
      quran,
      prophetic,
      istighfar,
      tasbih,
      tahmid,
      takbir,
      tahlil,
    ];
  }
}
